package entity

import (
	"strconv"
	"sync"
	"testing"
	"time"

	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"github.com/stretchr/testify/assert"
)

func TestReference_SaveOrGetIndex(t *testing.T) {
	r := &Reference{
		List:    iris.Reference{},
		IndexOf: map[string]int{},
	}
	for i := 0; i < 10; i++ {
		index := r.SaveOrGetIndex(iris.ReferenceItem{
			Title: "title" + strconv.Itoa(i),
			URI:   "url:" + strconv.Itoa(i),
		})
		assert.Equal(t, i+1, index)
	}
	index := r.SaveOrGetIndex(iris.ReferenceItem{
		Title: "title1",
		URI:   "url:1",
	})
	assert.Equal(t, 2, index)
}

func TestPlannerStore_GetTotalKnowledgeRecallTime(t *testing.T) {
	type fields struct {
		InitialRequirements        string
		Requirements               string
		EnhancedRequirements       string
		RelatedKnowledge           string
		ExperienceProgressPlan     string
		RelatedWorkspaceKnowledges []string
		ExperiencesHistory         string
		ActorsDesc                 any
		CurRound                   int
		knowledgeRecallTimePairs   []KnowledgeRecallTimePair
		knowledgeRecallMutex       sync.Mutex
	}
	tests := []struct {
		name   string
		fields fields
		want   time.Duration
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PlannerStore{
				InitialRequirements:        tt.fields.InitialRequirements,
				Requirements:               tt.fields.Requirements,
				EnhancedRequirements:       tt.fields.EnhancedRequirements,
				RelatedKnowledge:           tt.fields.RelatedKnowledge,
				ExperienceProgressPlan:     tt.fields.ExperienceProgressPlan,
				RelatedWorkspaceKnowledges: tt.fields.RelatedWorkspaceKnowledges,
				ExperiencesHistory:         tt.fields.ExperiencesHistory,
				ActorsDesc:                 tt.fields.ActorsDesc,
				CurRound:                   tt.fields.CurRound,
				knowledgeRecallTimePairs:   tt.fields.knowledgeRecallTimePairs,
				knowledgeRecallMutex:       tt.fields.knowledgeRecallMutex,
			}
			assert.Equalf(t, tt.want, p.GetTotalKnowledgeRecallTime(), "GetTotalKnowledgeRecallTime()")
		})
	}
}

func mustParse(value string) time.Time {
	t, err := time.Parse("2006-15-04 15:04:05", value)
	if err != nil {
		panic(err)
	}
	return t
}
