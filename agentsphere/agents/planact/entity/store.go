package entity

import (
	"code.byted.org/devgpt/kiwis/agentsphere/agents/iris"
	"sync"
	"time"
)

type PlannerStore struct {
	InitialRequirements        string
	Requirements               string
	EnhancedRequirements       string
	RelatedKnowledge           string
	ExperienceProgressPlan     string
	RelatedWorkspaceKnowledges []string
	ExperiencesHistory         string
	ActorsDesc                 any
	CurRound                   int
	// Knowledge recall timing tracking
	knowledgeRecallTimePairs []KnowledgeRecallTimePair
	knowledgeRecallMutex     sync.Mutex
}

type KnowledgeRecallTimePair struct {
	StartTime time.Time
	EndTime   time.Time
}

// RecordKnowledgeRecallTime records a single knowledge recall time pair
func (p *PlannerStore) RecordKnowledgeRecallTime(startTime, endTime time.Time) {
	p.knowledgeRecallMutex.Lock()
	defer p.knowledgeRecallMutex.Unlock()

	if p.knowledgeRecallTimePairs == nil {
		p.knowledgeRecallTimePairs = []KnowledgeRecallTimePair{}
	}
	p.knowledgeRecallTimePairs = append(p.knowledgeRecallTimePairs, KnowledgeRecallTimePair{
		StartTime: startTime,
		EndTime:   endTime,
	})
}

// GetTotalKnowledgeRecallTime returns the total time spent on knowledge recall
// This merges overlapping time intervals and calculates the total duration
func (p *PlannerStore) GetTotalKnowledgeRecallTime() time.Duration {
	p.knowledgeRecallMutex.Lock()
	defer p.knowledgeRecallMutex.Unlock()

	if len(p.knowledgeRecallTimePairs) == 0 {
		return 0
	}

	// Create a copy of the time pairs for sorting
	pairs := make([]KnowledgeRecallTimePair, len(p.knowledgeRecallTimePairs))
	copy(pairs, p.knowledgeRecallTimePairs)

	// Sort pairs by start time
	for i := 0; i < len(pairs)-1; i++ {
		for j := i + 1; j < len(pairs); j++ {
			if pairs[i].StartTime.After(pairs[j].StartTime) {
				pairs[i], pairs[j] = pairs[j], pairs[i]
			}
		}
	}

	// Merge overlapping intervals
	merged := []KnowledgeRecallTimePair{}
	for _, pair := range pairs {
		if len(merged) == 0 || merged[len(merged)-1].EndTime.Before(pair.StartTime) {
			// No overlap, add new interval
			merged = append(merged, pair)
		} else {
			// Overlap detected, merge intervals
			if merged[len(merged)-1].EndTime.Before(pair.EndTime) {
				merged[len(merged)-1].EndTime = pair.EndTime
			}
		}
	}

	// Calculate total duration from merged intervals
	var totalDuration time.Duration
	for _, pair := range merged {
		totalDuration += pair.EndTime.Sub(pair.StartTime)
	}

	return totalDuration
}

// ResetKnowledgeRecallTiming resets the knowledge recall timing
func (p *PlannerStore) ResetKnowledgeRecallTiming() {
	p.knowledgeRecallMutex.Lock()
	defer p.knowledgeRecallMutex.Unlock()
	p.knowledgeRecallTimePairs = []KnowledgeRecallTimePair{}
}

type HistoryTask struct {
	Task   string `json:"task"`
	Result string `json:"result"`
}

const (
	PlannerStoreKey    = "planact_planner_store"
	PlanStoreKey       = "planact_plan_store"
	ReferenceStoreKey  = "planact_reference_store"
	PatchToolUploadKey = "planact_patch_upload_store"
)

type PlanStore struct {
	Plans map[int]iris.EventPlanUpdated
}

type Reference struct {
	List iris.Reference
	// IndexOf is the index of the reference in the list
	// Key is the url, value is the index in citation content.
	IndexOf map[string]int
}

func (r *Reference) SaveOrGetIndex(reference iris.ReferenceItem) int {
	if r.IndexOf[reference.URI] > 0 {
		return r.IndexOf[reference.URI]
	}
	index := len(r.List) + 1
	reference.ID = index
	r.List = append(r.List, reference)
	r.IndexOf[reference.URI] = index
	return index
}

type ReferenceStore struct {
	SearchedRef Reference
	CreatedRef  Reference
}

type ReferenceType string

const (
	ReferenceTypeSearched ReferenceType = "search"
	ReferenceTypeCreated  ReferenceType = "create"
)

type PatchToolUploadStore struct {
	Artifact *iris.ResultArtifact
	Files    map[string]struct{}
}
